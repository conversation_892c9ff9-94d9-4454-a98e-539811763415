"""
键盘监听模块
负责监听用户设定的触发按键
"""

import logging
from pynput import keyboard
from typing import Callable, Optional


class KeyboardListener:
    """键盘监听器类"""
    
    def __init__(self, trigger_key: str, callback: Callable):
        """
        初始化键盘监听器
        
        Args:
            trigger_key: 触发键名称 (如 'f1', 'space', 'ctrl+c' 等)
            callback: 按键触发时的回调函数
        """
        self.trigger_key = trigger_key.lower()
        self.callback = callback
        self.listener: Optional[keyboard.Listener] = None
        self.is_running = False
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
    def _parse_key(self, key_str: str) -> tuple:
        """
        解析按键字符串
        
        Args:
            key_str: 按键字符串
            
        Returns:
            tuple: (是否需要修饰键, 主键, 修饰键列表)
        """
        key_str = key_str.lower()
        
        # 处理组合键
        if '+' in key_str:
            parts = key_str.split('+')
            modifiers = parts[:-1]
            main_key = parts[-1]
            return True, main_key, modifiers
        else:
            return False, key_str, []
    
    def _key_matches(self, key) -> bool:
        """
        检查按键是否匹配触发键

        Args:
            key: 按下的键

        Returns:
            bool: 是否匹配
        """
        _, main_key, _ = self._parse_key(self.trigger_key)
        
        try:
            # 处理特殊键
            if hasattr(key, 'name'):
                key_name = key.name.lower()
            else:
                key_name = str(key).replace("'", "").lower()
            
            # 功能键映射
            function_keys = {
                'f1': keyboard.Key.f1, 'f2': keyboard.Key.f2, 'f3': keyboard.Key.f3,
                'f4': keyboard.Key.f4, 'f5': keyboard.Key.f5, 'f6': keyboard.Key.f6,
                'f7': keyboard.Key.f7, 'f8': keyboard.Key.f8, 'f9': keyboard.Key.f9,
                'f10': keyboard.Key.f10, 'f11': keyboard.Key.f11, 'f12': keyboard.Key.f12,
                'space': keyboard.Key.space, 'enter': keyboard.Key.enter,
                'esc': keyboard.Key.esc, 'tab': keyboard.Key.tab
            }
            
            if main_key in function_keys:
                return key == function_keys[main_key]
            else:
                return key_name == main_key
                
        except Exception as e:
            self.logger.error(f"按键匹配检查出错: {e}")
            return False
    
    def _on_key_press(self, key):
        """
        按键按下事件处理
        
        Args:
            key: 按下的键
        """
        try:
            if self._key_matches(key):
                self.logger.info(f"检测到触发键: {self.trigger_key}")
                self.callback()
        except Exception as e:
            self.logger.error(f"按键处理出错: {e}")
    
    def start(self):
        """开始监听键盘"""
        if self.is_running:
            self.logger.warning("键盘监听器已在运行")
            return
            
        try:
            self.listener = keyboard.Listener(on_press=self._on_key_press)
            self.listener.start()
            self.is_running = True
            self.logger.info(f"开始监听触发键: {self.trigger_key}")
        except Exception as e:
            self.logger.error(f"启动键盘监听器失败: {e}")
            raise
    
    def stop(self):
        """停止监听键盘"""
        if self.listener and self.is_running:
            self.listener.stop()
            self.is_running = False
            self.logger.info("键盘监听器已停止")
    
    def is_alive(self) -> bool:
        """检查监听器是否运行中"""
        return self.is_running and self.listener is not None and self.listener.running
