"""
依赖安装脚本
自动安装项目所需的依赖包
"""

import subprocess
import sys
import os

def install_dependencies():
    """安装依赖包"""
    print("🔧 正在安装依赖包...")
    
    try:
        # 升级pip
        print("📦 升级pip...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # 安装依赖
        print("📦 安装项目依赖...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        
        print("✅ 依赖安装完成!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败: {e}")
        return False
    except FileNotFoundError:
        print("❌ 找不到 requirements.txt 文件")
        return False

def main():
    """主函数"""
    print("🤖 自动化网页点击工具 - 依赖安装")
    print("="*50)
    
    if install_dependencies():
        print("\n🎉 安装完成! 现在可以运行程序了:")
        print("python start.py")
    else:
        print("\n❌ 安装失败，请手动安装依赖:")
        print("pip install -r requirements.txt")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
