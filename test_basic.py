"""
基础功能测试脚本
用于验证各个模块的基本功能
"""

import json
import logging
import time
from web_clicker import WebClicker
from keyboard_listener import KeyboardListener

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_config_loading():
    """测试配置文件加载"""
    logger.info("=== 测试配置文件加载 ===")
    
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        logger.info("✓ 配置文件加载成功")
        logger.info(f"URL: {config.get('url')}")
        logger.info(f"触发键: {config.get('trigger_key')}")
        logger.info(f"点击序列数量: {len(config.get('click_sequence', []))}")
        return True
        
    except Exception as e:
        logger.error(f"✗ 配置文件加载失败: {e}")
        return False

def test_web_clicker():
    """测试网页点击器"""
    logger.info("=== 测试网页点击器 ===")
    
    try:
        # 加载配置
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 创建点击器实例
        clicker = WebClicker(config)
        
        # 打开网页
        url = config['url']
        logger.info(f"正在打开网页: {url}")
        clicker.open_url(url)
        
        logger.info("✓ 网页打开成功")
        logger.info(f"当前URL: {clicker.get_current_url()}")
        
        # 等待几秒让用户看到页面
        time.sleep(3)
        
        # 关闭浏览器
        clicker.close()
        logger.info("✓ 浏览器已关闭")
        return True
        
    except Exception as e:
        logger.error(f"✗ 网页点击器测试失败: {e}")
        return False

def test_keyboard_listener():
    """测试键盘监听器"""
    logger.info("=== 测试键盘监听器 ===")
    
    try:
        # 加载配置
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        trigger_key = config.get('trigger_key', 'f1')
        
        def on_key_pressed():
            logger.info(f"✓ 检测到触发键: {trigger_key}")
        
        # 创建键盘监听器
        listener = KeyboardListener(trigger_key, on_key_pressed)
        
        # 启动监听
        listener.start()
        logger.info(f"键盘监听器已启动，请按 {trigger_key.upper()} 键测试")
        logger.info("5秒后自动停止测试...")
        
        # 等待5秒
        time.sleep(5)
        
        # 停止监听
        listener.stop()
        logger.info("✓ 键盘监听器测试完成")
        return True
        
    except Exception as e:
        logger.error(f"✗ 键盘监听器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("开始基础功能测试")
    
    tests = [
        ("配置文件加载", test_config_loading),
        ("网页点击器", test_web_clicker),
        ("键盘监听器", test_keyboard_listener)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"测试 {test_name} 出现异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    logger.info(f"\n{'='*50}")
    logger.info("测试结果汇总:")
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        logger.info(f"{test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    logger.info(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！可以运行主程序了。")
    else:
        logger.warning("⚠️ 部分测试失败，请检查配置和环境。")

if __name__ == "__main__":
    main()
