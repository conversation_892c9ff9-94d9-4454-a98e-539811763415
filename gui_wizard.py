"""
配置向导对话框
帮助新用户快速设置自动点击工具
"""

import tkinter as tk
from tkinter import messagebox
from typing import Dict, Any, Optional
from gui_theme import ModernTheme
from gui_components import ModernButton, ModernEntry


class ConfigWizard:
    """配置向导类"""
    
    def __init__(self, parent, theme: ModernTheme):
        self.parent = parent
        self.theme = theme
        self.config = {}
        self.current_step = 0
        self.steps = [
            self._step_welcome,
            self._step_basic_config,
            self._step_add_elements,
            self._step_finish
        ]
        
        self._create_dialog()
    
    def _create_dialog(self):
        """创建向导对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("配置向导")
        self.dialog.geometry("600x500")
        self.dialog.resizable(False, False)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (500 // 2)
        self.dialog.geometry(f"600x500+{x}+{y}")
        
        self.theme.apply_frame_style(self.dialog)
        self.dialog.configure(bg=self.theme.get_color('bg_primary'))
        
        self._create_layout()
        self._show_current_step()
    
    def _create_layout(self):
        """创建布局"""
        # 主容器
        main_frame = tk.Frame(self.dialog)
        self.theme.apply_frame_style(main_frame)
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # 标题区域
        title_frame = tk.Frame(main_frame)
        self.theme.apply_frame_style(title_frame)
        title_frame.pack(fill='x', pady=(0, 20))
        
        self.title_label = tk.Label(title_frame, text="配置向导")
        self.theme.apply_label_style(self.title_label, 'heading')
        self.title_label.pack(side=tk.LEFT)
        
        self.step_label = tk.Label(title_frame, text="1/4")
        self.theme.apply_label_style(self.step_label, 'small')
        self.step_label.pack(side=tk.RIGHT)
        
        # 内容区域
        self.content_frame = tk.Frame(main_frame)
        self.theme.apply_frame_style(self.content_frame)
        self.content_frame.pack(fill='both', expand=True, pady=(0, 20))
        
        # 按钮区域
        btn_frame = tk.Frame(main_frame)
        self.theme.apply_frame_style(btn_frame)
        btn_frame.pack(fill='x')
        
        self.prev_btn = ModernButton(btn_frame, self.theme, 'secondary',
                                    text="上一步", command=self._prev_step, state='disabled')
        self.prev_btn.pack(side=tk.LEFT)
        
        self.cancel_btn = ModernButton(btn_frame, self.theme, 'secondary',
                                      text="取消", command=self._cancel)
        self.cancel_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        self.next_btn = ModernButton(btn_frame, self.theme, 'primary',
                                    text="下一步", command=self._next_step)
        self.next_btn.pack(side=tk.RIGHT)
    
    def _show_current_step(self):
        """显示当前步骤"""
        # 清空内容区域
        for widget in self.content_frame.winfo_children():
            widget.destroy()
        
        # 更新步骤标签
        self.step_label.configure(text=f"{self.current_step + 1}/{len(self.steps)}")
        
        # 显示当前步骤内容
        self.steps[self.current_step]()
        
        # 更新按钮状态
        self.prev_btn.configure(state='normal' if self.current_step > 0 else 'disabled')
        
        if self.current_step == len(self.steps) - 1:
            self.next_btn.configure(text="完成")
        else:
            self.next_btn.configure(text="下一步")
    
    def _step_welcome(self):
        """欢迎步骤"""
        self.title_label.configure(text="欢迎使用配置向导")
        
        welcome_text = """
欢迎使用自动化网页点击工具配置向导！

本向导将帮助您快速设置：
• 目标网页地址
• 触发按键和浏览器设置
• 点击序列配置

让我们开始配置您的自动点击工具吧！
        """
        
        welcome_label = tk.Label(self.content_frame, text=welcome_text.strip(),
                                justify=tk.LEFT, wraplength=500)
        self.theme.apply_label_style(welcome_label, 'default')
        welcome_label.pack(pady=50)
        
        # 示例配置按钮
        example_btn = ModernButton(self.content_frame, self.theme, 'secondary',
                                  text="加载示例配置", command=self._load_example)
        example_btn.pack(pady=20)
    
    def _step_basic_config(self):
        """基本配置步骤"""
        self.title_label.configure(text="基本配置")
        
        # URL输入
        self.url_entry = ModernEntry(self.content_frame, self.theme,
                                    label="目标网页URL *", 
                                    placeholder="https://example.com")
        self.url_entry.pack(fill='x', pady=(0, 20))
        
        # 设置框架
        settings_frame = tk.Frame(self.content_frame)
        self.theme.apply_frame_style(settings_frame)
        settings_frame.pack(fill='x', pady=(0, 20))
        
        # 左列
        left_col = tk.Frame(settings_frame)
        self.theme.apply_frame_style(left_col)
        left_col.pack(side=tk.LEFT, fill='x', expand=True, padx=(0, 10))
        
        trigger_label = tk.Label(left_col, text="触发按键")
        self.theme.apply_label_style(trigger_label, 'small')
        trigger_label.pack(anchor='w', pady=(0, 4))
        
        self.trigger_var = tk.StringVar(value="f1")
        trigger_frame = tk.Frame(left_col)
        self.theme.apply_frame_style(trigger_frame)
        trigger_frame.pack(fill='x')
        
        for key in ['f1', 'f2', 'f3', 'space']:
            rb = tk.Radiobutton(trigger_frame, text=key.upper(), variable=self.trigger_var,
                               value=key, font=self.theme.get_font('small'))
            rb.configure(bg=self.theme.get_color('bg_primary'),
                        fg=self.theme.get_color('text_primary'),
                        selectcolor=self.theme.get_color('accent'))
            rb.pack(side=tk.LEFT, padx=(0, 10))
        
        # 右列
        right_col = tk.Frame(settings_frame)
        self.theme.apply_frame_style(right_col)
        right_col.pack(side=tk.LEFT, fill='x', expand=True)
        
        browser_label = tk.Label(right_col, text="浏览器")
        self.theme.apply_label_style(browser_label, 'small')
        browser_label.pack(anchor='w', pady=(0, 4))
        
        self.browser_var = tk.StringVar(value="chrome")
        browser_frame = tk.Frame(right_col)
        self.theme.apply_frame_style(browser_frame)
        browser_frame.pack(fill='x')
        
        for browser in ['chrome', 'firefox']:
            rb = tk.Radiobutton(browser_frame, text=browser.title(), 
                               variable=self.browser_var, value=browser,
                               font=self.theme.get_font('small'))
            rb.configure(bg=self.theme.get_color('bg_primary'),
                        fg=self.theme.get_color('text_primary'),
                        selectcolor=self.theme.get_color('accent'))
            rb.pack(side=tk.LEFT, padx=(0, 10))
        
        # 超时设置
        timeout_label = tk.Label(self.content_frame, text="等待超时时间（秒）")
        self.theme.apply_label_style(timeout_label, 'small')
        timeout_label.pack(anchor='w', pady=(20, 4))
        
        self.timeout_var = tk.IntVar(value=10)
        timeout_scale = tk.Scale(self.content_frame, from_=5, to=30, orient=tk.HORIZONTAL,
                                variable=self.timeout_var, length=300)
        timeout_scale.configure(bg=self.theme.get_color('bg_primary'),
                               fg=self.theme.get_color('text_primary'))
        timeout_scale.pack(anchor='w')
    
    def _step_add_elements(self):
        """添加元素步骤"""
        self.title_label.configure(text="配置点击序列")
        
        info_text = """
现在配置点击序列。至少需要添加一个元素。

提示：
• 第一个元素会直接点击
• 第二个元素会等待出现后再点击
• 后续元素按顺序依次点击
        """
        
        info_label = tk.Label(self.content_frame, text=info_text.strip(),
                             justify=tk.LEFT, wraplength=500)
        self.theme.apply_label_style(info_label, 'small')
        info_label.pack(pady=(0, 20))
        
        # 元素列表
        self.elements_frame = tk.Frame(self.content_frame)
        self.theme.apply_frame_style(self.elements_frame)
        self.elements_frame.pack(fill='both', expand=True, pady=(0, 20))
        
        # 添加按钮
        add_btn = ModernButton(self.content_frame, self.theme, 'primary',
                              text="+ 添加元素", command=self._add_element)
        add_btn.pack()
        
        # 初始化元素列表
        self.elements = []
        self._refresh_elements()
    
    def _step_finish(self):
        """完成步骤"""
        self.title_label.configure(text="配置完成")
        
        finish_text = """
恭喜！配置已完成。

配置摘要：
        """
        
        # 显示配置摘要
        summary = f"""
• 目标网页: {self.config.get('url', '未设置')}
• 触发按键: {self.config.get('trigger_key', 'f1').upper()}
• 浏览器: {self.config.get('browser', 'chrome').title()}
• 等待超时: {self.config.get('wait_timeout', 10)}秒
• 点击元素: {len(self.config.get('click_sequence', []))}个
        """
        
        finish_label = tk.Label(self.content_frame, text=finish_text + summary,
                               justify=tk.LEFT, wraplength=500)
        self.theme.apply_label_style(finish_label, 'default')
        finish_label.pack(pady=30)
    
    def _load_example(self):
        """加载示例配置"""
        example_config = {
            'url': 'https://httpbin.org/forms/post',
            'trigger_key': 'f1',
            'browser': 'chrome',
            'wait_timeout': 10,
            'click_sequence': [
                {
                    'name': '客户名称输入框',
                    'xpath': "//input[@name='custname']",
                    'description': '点击客户名称输入框'
                },
                {
                    'name': '提交按钮',
                    'xpath': "//input[@type='submit']",
                    'description': '点击提交按钮'
                }
            ]
        }
        
        if messagebox.askyesno("加载示例", "这将覆盖当前配置，确定要加载示例配置吗？"):
            self.config = example_config
            messagebox.showinfo("成功", "示例配置已加载")
    
    def _add_element(self):
        """添加元素"""
        # 这里可以打开一个简化的元素编辑对话框
        # 为了简化，我们直接添加一个示例元素
        element = {
            'name': f'元素{len(self.elements) + 1}',
            'xpath': f"//button[@id='btn{len(self.elements) + 1}']",
            'description': f'点击第{len(self.elements) + 1}个按钮'
        }
        self.elements.append(element)
        self._refresh_elements()
    
    def _refresh_elements(self):
        """刷新元素列表显示"""
        for widget in self.elements_frame.winfo_children():
            widget.destroy()
        
        for i, element in enumerate(self.elements):
            item_frame = tk.Frame(self.elements_frame)
            self.theme.apply_frame_style(item_frame)
            item_frame.pack(fill='x', pady=2)
            
            label = tk.Label(item_frame, text=f"{i+1}. {element['name']}")
            self.theme.apply_label_style(label, 'small')
            label.pack(side=tk.LEFT)
            
            del_btn = ModernButton(item_frame, self.theme, 'error',
                                  text="删除", command=lambda idx=i: self._delete_element(idx))
            del_btn.pack(side=tk.RIGHT)
    
    def _delete_element(self, index: int):
        """删除元素"""
        if 0 <= index < len(self.elements):
            del self.elements[index]
            self._refresh_elements()
    
    def _next_step(self):
        """下一步"""
        if self.current_step == 1:  # 基本配置步骤
            if not self.url_entry.get():
                messagebox.showerror("错误", "请输入目标网页URL")
                return
            
            self.config.update({
                'url': self.url_entry.get(),
                'trigger_key': self.trigger_var.get(),
                'browser': self.browser_var.get(),
                'wait_timeout': self.timeout_var.get()
            })
        
        elif self.current_step == 2:  # 添加元素步骤
            if not self.elements:
                messagebox.showerror("错误", "请至少添加一个点击元素")
                return
            
            self.config['click_sequence'] = self.elements
        
        elif self.current_step == len(self.steps) - 1:  # 完成步骤
            self.dialog.destroy()
            return
        
        self.current_step += 1
        self._show_current_step()
    
    def _prev_step(self):
        """上一步"""
        if self.current_step > 0:
            self.current_step -= 1
            self._show_current_step()
    
    def _cancel(self):
        """取消向导"""
        if messagebox.askyesno("取消", "确定要取消配置向导吗？"):
            self.config = None
            self.dialog.destroy()
    
    def show(self) -> Optional[Dict[str, Any]]:
        """显示向导并返回配置"""
        self.dialog.wait_window()
        return self.config
