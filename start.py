"""
启动脚本 - 提供用户友好的启动界面
"""

import os
import sys
import json
import subprocess
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major != 3 or version.minor < 8:
        print("❌ 错误: 需要 Python 3.8 或更高版本")
        print(f"当前版本: Python {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python 版本检查通过: {version.major}.{version.minor}.{version.micro}")
    return True

def check_dependencies():
    """检查依赖包"""
    print("🔍 检查依赖包...")
    
    required_packages = ['selenium', 'pynput', 'webdriver-manager']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} (未安装)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def check_config():
    """检查配置文件"""
    print("🔍 检查配置文件...")
    
    config_file = Path("config.json")
    if not config_file.exists():
        print("❌ config.json 文件不存在")
        
        # 询问是否创建示例配置
        response = input("是否创建示例配置文件? (y/n): ").lower().strip()
        if response == 'y':
            try:
                # 复制示例配置
                import shutil
                shutil.copy("config_example.json", "config.json")
                print("✅ 已创建示例配置文件 config.json")
                print("请编辑 config.json 文件设置您的目标网页和点击序列")
                return False
            except Exception as e:
                print(f"❌ 创建配置文件失败: {e}")
                return False
        else:
            return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查必需字段
        required_fields = ['url', 'trigger_key', 'click_sequence']
        for field in required_fields:
            if field not in config:
                print(f"❌ 配置文件缺少字段: {field}")
                return False
        
        print("✅ 配置文件检查通过")
        print(f"   目标网页: {config['url']}")
        print(f"   触发按键: {config['trigger_key'].upper()}")
        print(f"   点击序列: {len(config['click_sequence'])} 个元素")
        return True
        
    except json.JSONDecodeError:
        print("❌ 配置文件格式错误")
        return False
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False

def show_menu():
    """显示主菜单"""
    print("\n" + "="*50)
    print("🤖 自动化网页点击工具")
    print("="*50)
    print("1. 运行基础测试")
    print("2. 启动主程序")
    print("3. 编辑配置文件")
    print("4. 查看帮助")
    print("5. 退出")
    print("="*50)

def run_test():
    """运行基础测试"""
    print("\n🧪 运行基础测试...")
    try:
        subprocess.run([sys.executable, "test_basic.py"], check=True)
    except subprocess.CalledProcessError:
        print("❌ 测试失败")
    except FileNotFoundError:
        print("❌ 找不到测试文件")

def run_main():
    """运行主程序"""
    print("\n🚀 启动主程序...")
    try:
        subprocess.run([sys.executable, "main.py"], check=True)
    except subprocess.CalledProcessError:
        print("❌ 程序运行失败")
    except KeyboardInterrupt:
        print("\n程序已停止")
    except FileNotFoundError:
        print("❌ 找不到主程序文件")

def edit_config():
    """编辑配置文件"""
    config_file = "config.json"
    
    if not Path(config_file).exists():
        print("❌ 配置文件不存在")
        return
    
    print(f"\n📝 请使用文本编辑器打开 {config_file} 文件进行编辑")
    print("配置完成后请保存文件并重新运行程序")
    
    # 尝试用系统默认编辑器打开
    try:
        if os.name == 'nt':  # Windows
            os.startfile(config_file)
        elif os.name == 'posix':  # macOS/Linux
            subprocess.run(['open' if sys.platform == 'darwin' else 'xdg-open', config_file])
    except:
        print(f"请手动打开 {config_file} 文件进行编辑")

def show_help():
    """显示帮助信息"""
    print("\n📖 使用帮助")
    print("="*50)
    print("1. 首次使用请先运行基础测试确保环境正常")
    print("2. 编辑 config.json 文件设置目标网页和点击序列")
    print("3. 启动主程序后按配置的触发键执行点击序列")
    print("4. 按 Ctrl+C 可以随时退出程序")
    print("\n📋 配置说明:")
    print("- url: 目标网页地址")
    print("- trigger_key: 触发键 (如 f1, f2, space 等)")
    print("- click_sequence: 点击序列配置")
    print("- 每个元素需要配置 name, xpath, description")
    print("\n🔧 获取XPath方法:")
    print("- Chrome: 右键元素 → 检查 → 右键HTML → Copy → Copy XPath")
    print("- Firefox: 右键元素 → 检查元素 → 右键HTML → 复制 → XPath")

def main():
    """主函数"""
    print("🤖 自动化网页点击工具启动器")
    print("="*50)
    
    # 环境检查
    if not check_python_version():
        input("按回车键退出...")
        return
    
    if not check_dependencies():
        input("按回车键退出...")
        return
    
    # 主循环
    while True:
        show_menu()
        
        try:
            choice = input("\n请选择操作 (1-5): ").strip()
            
            if choice == '1':
                run_test()
            elif choice == '2':
                if check_config():
                    run_main()
                else:
                    print("请先配置 config.json 文件")
            elif choice == '3':
                edit_config()
            elif choice == '4':
                show_help()
            elif choice == '5':
                print("👋 再见!")
                break
            else:
                print("❌ 无效选择，请输入 1-5")
                
        except KeyboardInterrupt:
            print("\n👋 再见!")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()
