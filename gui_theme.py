"""
GUI主题和样式配置
实现Material Design风格的现代化界面
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Any


class ModernTheme:
    """现代化主题配置类"""
    
    # Material Design 配色方案
    LIGHT_THEME = {
        'bg_primary': '#FFFFFF',
        'bg_secondary': '#F5F5F5',
        'bg_surface': '#FAFAFA',
        'bg_card': '#FFFFFF',
        'text_primary': '#212121',
        'text_secondary': '#757575',
        'text_hint': '#9E9E9E',
        'accent': '#2196F3',
        'accent_dark': '#1976D2',
        'success': '#4CAF50',
        'warning': '#FF9800',
        'error': '#F44336',
        'border': '#E0E0E0',
        'shadow': '#00000020',
        'hover': '#F0F0F0'
    }
    
    DARK_THEME = {
        'bg_primary': '#121212',
        'bg_secondary': '#1E1E1E',
        'bg_surface': '#2D2D2D',
        'bg_card': '#1E1E1E',
        'text_primary': '#FFFFFF',
        'text_secondary': '#B3B3B3',
        'text_hint': '#666666',
        'accent': '#64B5F6',
        'accent_dark': '#42A5F5',
        'success': '#81C784',
        'warning': '#FFB74D',
        'error': '#E57373',
        'border': '#404040',
        'shadow': '#00000040',
        'hover': '#333333'
    }
    
    def __init__(self, dark_mode: bool = False):
        """
        初始化主题
        
        Args:
            dark_mode: 是否使用深色主题
        """
        self.dark_mode = dark_mode
        self.colors = self.DARK_THEME if dark_mode else self.LIGHT_THEME
        
        # 字体配置
        self.fonts = {
            'default': ('Segoe UI', 9),
            'heading': ('Segoe UI', 12, 'bold'),
            'subheading': ('Segoe UI', 10, 'bold'),
            'small': ('Segoe UI', 8),
            'monospace': ('Consolas', 9)
        }
        
        # 组件样式
        self.styles = {
            'button': {
                'relief': 'flat',
                'borderwidth': 0,
                'padx': 20,
                'pady': 8,
                'font': self.fonts['default']
            },
            'entry': {
                'relief': 'flat',
                'borderwidth': 1,
                'highlightthickness': 2,
                'font': self.fonts['default']
            },
            'frame': {
                'relief': 'flat',
                'borderwidth': 0
            },
            'label': {
                'font': self.fonts['default']
            }
        }
    
    def get_color(self, key: str) -> str:
        """获取颜色值"""
        return self.colors.get(key, '#000000')
    
    def get_font(self, key: str) -> tuple:
        """获取字体配置"""
        return self.fonts.get(key, self.fonts['default'])
    
    def apply_button_style(self, button: tk.Button, style: str = 'primary'):
        """
        应用按钮样式
        
        Args:
            button: 按钮组件
            style: 样式类型 (primary, secondary, success, warning, error)
        """
        base_style = self.styles['button'].copy()
        
        if style == 'primary':
            base_style.update({
                'bg': self.get_color('accent'),
                'fg': '#FFFFFF',
                'activebackground': self.get_color('accent_dark'),
                'activeforeground': '#FFFFFF'
            })
        elif style == 'secondary':
            base_style.update({
                'bg': self.get_color('bg_secondary'),
                'fg': self.get_color('text_primary'),
                'activebackground': self.get_color('hover'),
                'activeforeground': self.get_color('text_primary')
            })
        elif style == 'success':
            base_style.update({
                'bg': self.get_color('success'),
                'fg': '#FFFFFF',
                'activebackground': '#45A049',
                'activeforeground': '#FFFFFF'
            })
        elif style == 'warning':
            base_style.update({
                'bg': self.get_color('warning'),
                'fg': '#FFFFFF',
                'activebackground': '#F57C00',
                'activeforeground': '#FFFFFF'
            })
        elif style == 'error':
            base_style.update({
                'bg': self.get_color('error'),
                'fg': '#FFFFFF',
                'activebackground': '#D32F2F',
                'activeforeground': '#FFFFFF'
            })
        
        button.configure(**base_style)
    
    def apply_entry_style(self, entry: tk.Entry):
        """应用输入框样式"""
        style = self.styles['entry'].copy()
        style.update({
            'bg': self.get_color('bg_card'),
            'fg': self.get_color('text_primary'),
            'insertbackground': self.get_color('text_primary'),
            'highlightcolor': self.get_color('accent'),
            'highlightbackground': self.get_color('border')
        })
        entry.configure(**style)
    
    def apply_frame_style(self, frame: tk.Frame, elevated: bool = False):
        """
        应用框架样式
        
        Args:
            frame: 框架组件
            elevated: 是否显示阴影效果
        """
        style = self.styles['frame'].copy()
        bg_color = self.get_color('bg_card') if elevated else self.get_color('bg_primary')
        style.update({
            'bg': bg_color
        })
        frame.configure(**style)
    
    def apply_label_style(self, label: tk.Label, style: str = 'default'):
        """
        应用标签样式
        
        Args:
            label: 标签组件
            style: 样式类型 (default, heading, subheading, small, hint)
        """
        base_style = self.styles['label'].copy()
        
        if style == 'heading':
            base_style.update({
                'font': self.get_font('heading'),
                'fg': self.get_color('text_primary')
            })
        elif style == 'subheading':
            base_style.update({
                'font': self.get_font('subheading'),
                'fg': self.get_color('text_primary')
            })
        elif style == 'small':
            base_style.update({
                'font': self.get_font('small'),
                'fg': self.get_color('text_secondary')
            })
        elif style == 'hint':
            base_style.update({
                'font': self.get_font('small'),
                'fg': self.get_color('text_hint')
            })
        else:
            base_style.update({
                'fg': self.get_color('text_primary')
            })
        
        base_style['bg'] = self.get_color('bg_primary')
        label.configure(**base_style)
    
    def create_card_frame(self, parent: tk.Widget, **kwargs) -> tk.Frame:
        """
        创建卡片样式的框架
        
        Args:
            parent: 父组件
            **kwargs: 额外的配置参数
            
        Returns:
            tk.Frame: 卡片框架
        """
        frame = tk.Frame(parent, **kwargs)
        self.apply_frame_style(frame, elevated=True)
        frame.configure(
            relief='flat',
            borderwidth=1,
            highlightbackground=self.get_color('border'),
            highlightthickness=1
        )
        return frame
    
    def toggle_theme(self):
        """切换主题"""
        self.dark_mode = not self.dark_mode
        self.colors = self.DARK_THEME if self.dark_mode else self.LIGHT_THEME
