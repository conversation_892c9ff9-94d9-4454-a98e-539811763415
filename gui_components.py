"""
GUI自定义组件
包含各种现代化的UI组件
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from typing import Callable, Optional, List, Dict, Any
import json
from gui_theme import ModernTheme


class StatusIndicator(tk.Frame):
    """状态指示器组件"""
    
    def __init__(self, parent, theme: ModernTheme, **kwargs):
        super().__init__(parent, **kwargs)
        self.theme = theme
        self.theme.apply_frame_style(self)
        
        # 状态圆点
        self.canvas = tk.Canvas(self, width=12, height=12, highlightthickness=0)
        self.canvas.pack(side=tk.LEFT, padx=(0, 8))
        self.theme.apply_frame_style(self.canvas)
        
        # 状态文本
        self.status_label = tk.Label(self, text="未连接")
        self.theme.apply_label_style(self.status_label, 'small')
        self.status_label.pack(side=tk.LEFT)
        
        self.set_status('idle')
    
    def set_status(self, status: str, message: str = None):
        """
        设置状态
        
        Args:
            status: 状态类型 (idle, running, success, error)
            message: 状态消息
        """
        self.canvas.delete("all")
        
        colors = {
            'idle': self.theme.get_color('text_hint'),
            'running': self.theme.get_color('warning'),
            'success': self.theme.get_color('success'),
            'error': self.theme.get_color('error')
        }
        
        messages = {
            'idle': '等待中',
            'running': '运行中',
            'success': '成功',
            'error': '错误'
        }
        
        color = colors.get(status, colors['idle'])
        text = message or messages.get(status, '未知')
        
        # 绘制状态圆点
        self.canvas.create_oval(2, 2, 10, 10, fill=color, outline=color)
        
        # 更新状态文本
        self.status_label.configure(text=text)


class ModernButton(tk.Button):
    """现代化按钮组件"""
    
    def __init__(self, parent, theme: ModernTheme, style: str = 'primary', **kwargs):
        super().__init__(parent, **kwargs)
        self.theme = theme
        self.style_type = style
        self.theme.apply_button_style(self, style)
        
        # 添加悬停效果
        self.bind("<Enter>", self._on_enter)
        self.bind("<Leave>", self._on_leave)
    
    def _on_enter(self, event):
        """鼠标悬停效果"""
        if self.style_type == 'primary':
            self.configure(bg=self.theme.get_color('accent_dark'))
        else:
            self.configure(bg=self.theme.get_color('hover'))
    
    def _on_leave(self, event):
        """鼠标离开效果"""
        self.theme.apply_button_style(self, self.style_type)


class ModernEntry(tk.Frame):
    """现代化输入框组件"""
    
    def __init__(self, parent, theme: ModernTheme, label: str = "", placeholder: str = "", **kwargs):
        super().__init__(parent, **kwargs)
        self.theme = theme
        self.theme.apply_frame_style(self)
        
        # 标签
        if label:
            self.label = tk.Label(self, text=label)
            self.theme.apply_label_style(self.label, 'small')
            self.label.pack(anchor='w', pady=(0, 4))
        
        # 输入框
        self.entry = tk.Entry(self)
        self.theme.apply_entry_style(self.entry)
        self.entry.pack(fill='x', ipady=8)
        
        # 占位符
        self.placeholder = placeholder
        if placeholder:
            self._show_placeholder()
            self.entry.bind("<FocusIn>", self._hide_placeholder)
            self.entry.bind("<FocusOut>", self._show_placeholder)
    
    def _show_placeholder(self, event=None):
        """显示占位符"""
        if not self.entry.get():
            self.entry.insert(0, self.placeholder)
            self.entry.configure(fg=self.theme.get_color('text_hint'))
    
    def _hide_placeholder(self, event=None):
        """隐藏占位符"""
        if self.entry.get() == self.placeholder:
            self.entry.delete(0, tk.END)
            self.entry.configure(fg=self.theme.get_color('text_primary'))
    
    def get(self) -> str:
        """获取输入值"""
        value = self.entry.get()
        return "" if value == self.placeholder else value
    
    def set(self, value: str):
        """设置输入值"""
        self.entry.delete(0, tk.END)
        if value:
            self.entry.insert(0, value)
            self.entry.configure(fg=self.theme.get_color('text_primary'))
        else:
            self._show_placeholder()


class ClickSequenceList(tk.Frame):
    """点击序列列表组件"""
    
    def __init__(self, parent, theme: ModernTheme, **kwargs):
        super().__init__(parent, **kwargs)
        self.theme = theme
        self.theme.apply_frame_style(self)
        
        self.items: List[Dict[str, str]] = []
        self.callbacks = {
            'edit': None,
            'delete': None,
            'reorder': None
        }
        
        # 创建列表框架
        self._create_list_frame()
    
    def _create_list_frame(self):
        """创建列表框架"""
        # 标题
        title_frame = tk.Frame(self)
        self.theme.apply_frame_style(title_frame)
        title_frame.pack(fill='x', pady=(0, 10))
        
        title_label = tk.Label(title_frame, text="点击序列")
        self.theme.apply_label_style(title_label, 'subheading')
        title_label.pack(side=tk.LEFT)
        
        # 添加按钮
        add_btn = ModernButton(title_frame, self.theme, 'primary', 
                              text="+ 添加", command=self._add_item)
        add_btn.pack(side=tk.RIGHT)
        
        # 列表容器
        self.list_frame = tk.Frame(self)
        self.theme.apply_frame_style(self.list_frame)
        self.list_frame.pack(fill='both', expand=True)
        
        # 滚动条
        self.canvas = tk.Canvas(self.list_frame, highlightthickness=0)
        self.scrollbar = ttk.Scrollbar(self.list_frame, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = tk.Frame(self.canvas)
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )
        
        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=self.scrollbar.set)
        
        self.canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")
        
        self.theme.apply_frame_style(self.canvas)
        self.theme.apply_frame_style(self.scrollable_frame)
    
    def _add_item(self):
        """添加新项目"""
        if self.callbacks['edit']:
            self.callbacks['edit'](None, len(self.items))
    
    def set_callbacks(self, edit_callback: Callable = None, 
                     delete_callback: Callable = None,
                     reorder_callback: Callable = None):
        """设置回调函数"""
        if edit_callback:
            self.callbacks['edit'] = edit_callback
        if delete_callback:
            self.callbacks['delete'] = delete_callback
        if reorder_callback:
            self.callbacks['reorder'] = reorder_callback
    
    def update_items(self, items: List[Dict[str, str]]):
        """更新列表项目"""
        self.items = items
        self._refresh_list()
    
    def _refresh_list(self):
        """刷新列表显示"""
        # 清除现有项目
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()
        
        # 添加新项目
        for i, item in enumerate(self.items):
            self._create_list_item(i, item)
    
    def _create_list_item(self, index: int, item: Dict[str, str]):
        """创建列表项目"""
        item_frame = self.theme.create_card_frame(self.scrollable_frame)
        item_frame.pack(fill='x', pady=2, padx=2)
        
        # 内容框架
        content_frame = tk.Frame(item_frame)
        self.theme.apply_frame_style(content_frame)
        content_frame.pack(fill='x', padx=10, pady=8)
        
        # 序号
        index_label = tk.Label(content_frame, text=f"{index + 1}.")
        self.theme.apply_label_style(index_label, 'small')
        index_label.pack(side=tk.LEFT, padx=(0, 8))
        
        # 项目信息
        info_frame = tk.Frame(content_frame)
        self.theme.apply_frame_style(info_frame)
        info_frame.pack(side=tk.LEFT, fill='x', expand=True)
        
        name_label = tk.Label(info_frame, text=item.get('name', '未命名'))
        self.theme.apply_label_style(name_label, 'default')
        name_label.pack(anchor='w')
        
        desc_label = tk.Label(info_frame, text=item.get('description', ''))
        self.theme.apply_label_style(desc_label, 'hint')
        desc_label.pack(anchor='w')
        
        # 操作按钮
        btn_frame = tk.Frame(content_frame)
        self.theme.apply_frame_style(btn_frame)
        btn_frame.pack(side=tk.RIGHT)
        
        edit_btn = ModernButton(btn_frame, self.theme, 'secondary', 
                               text="编辑", width=6,
                               command=lambda: self.callbacks['edit'](item, index) if self.callbacks['edit'] else None)
        edit_btn.pack(side=tk.LEFT, padx=2)
        
        delete_btn = ModernButton(btn_frame, self.theme, 'error', 
                                 text="删除", width=6,
                                 command=lambda: self._delete_item(index))
        delete_btn.pack(side=tk.LEFT, padx=2)
    
    def _delete_item(self, index: int):
        """删除项目"""
        if messagebox.askyesno("确认删除", f"确定要删除第 {index + 1} 个项目吗？"):
            if self.callbacks['delete']:
                self.callbacks['delete'](index)
