"""
GUI版本启动脚本
提供图形界面和命令行界面的选择
"""

import sys
import tkinter as tk
from tkinter import messagebox
import subprocess
from pathlib import Path


def check_dependencies():
    """检查GUI依赖"""
    try:
        import tkinter
        return True
    except ImportError:
        return False


def show_interface_choice():
    """显示界面选择对话框"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    choice = messagebox.askyesnocancel(
        "选择界面",
        "请选择要使用的界面类型：\n\n"
        "是 - 图形界面 (GUI)\n"
        "否 - 命令行界面 (CLI)\n"
        "取消 - 退出程序",
        icon='question'
    )
    
    root.destroy()
    return choice


def run_gui():
    """运行GUI版本"""
    try:
        from gui_main import main
        main()
    except ImportError as e:
        messagebox.showerror("错误", f"GUI模块导入失败: {e}")
    except Exception as e:
        messagebox.showerror("错误", f"GUI启动失败: {e}")


def run_cli():
    """运行命令行版本"""
    try:
        subprocess.run([sys.executable, "start.py"], check=True)
    except subprocess.CalledProcessError:
        print("命令行版本启动失败")
    except FileNotFoundError:
        print("找不到命令行启动文件")


def main():
    """主函数"""
    print("🤖 自动化网页点击工具")
    print("=" * 30)
    
    # 检查GUI依赖
    if not check_dependencies():
        print("❌ GUI依赖不可用，启动命令行版本...")
        run_cli()
        return
    
    # 检查是否有命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == '--gui':
            print("🖥️ 启动图形界面...")
            run_gui()
        elif sys.argv[1] == '--cli':
            print("💻 启动命令行界面...")
            run_cli()
        elif sys.argv[1] == '--help':
            print("使用方法:")
            print("  python gui_start.py          # 显示选择对话框")
            print("  python gui_start.py --gui    # 直接启动GUI")
            print("  python gui_start.py --cli    # 直接启动CLI")
            print("  python gui_start.py --help   # 显示帮助")
        else:
            print("❌ 未知参数，使用 --help 查看帮助")
    else:
        # 显示选择对话框
        choice = show_interface_choice()
        
        if choice is True:  # GUI
            print("🖥️ 启动图形界面...")
            run_gui()
        elif choice is False:  # CLI
            print("💻 启动命令行界面...")
            run_cli()
        else:  # 取消
            print("👋 再见!")


if __name__ == "__main__":
    main()
