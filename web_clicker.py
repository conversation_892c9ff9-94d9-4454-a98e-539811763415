"""
网页自动点击核心模块
负责浏览器控制和点击序列执行
"""

import logging
import time
from typing import List, Dict, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.firefox.service import Service as FirefoxService
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
from webdriver_manager.firefox import GeckoDriverManager


class WebClicker:
    """网页自动点击器类"""
    
    def __init__(self, config: Dict):
        """
        初始化网页点击器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.driver: Optional[webdriver.Chrome | webdriver.Firefox] = None
        self.wait: Optional[WebDriverWait] = None
        self.is_executing = False
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
    def _setup_driver(self):
        """设置WebDriver"""
        browser = self.config.get('browser', 'chrome').lower()
        headless = self.config.get('headless', False)
        
        try:
            if browser == 'chrome':
                options = webdriver.ChromeOptions()
                if headless:
                    options.add_argument('--headless')
                options.add_argument('--no-sandbox')
                options.add_argument('--disable-dev-shm-usage')
                options.add_argument('--disable-gpu')
                
                service = ChromeService(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=options)
                
            elif browser == 'firefox':
                options = webdriver.FirefoxOptions()
                if headless:
                    options.add_argument('--headless')
                    
                service = FirefoxService(GeckoDriverManager().install())
                self.driver = webdriver.Firefox(service=service, options=options)
                
            else:
                raise ValueError(f"不支持的浏览器类型: {browser}")
            
            # 设置隐式等待
            implicit_wait = self.config.get('implicit_wait', 3)
            self.driver.implicitly_wait(implicit_wait)
            
            # 设置显式等待
            wait_timeout = self.config.get('wait_timeout', 10)
            self.wait = WebDriverWait(self.driver, wait_timeout)
            
            self.logger.info(f"WebDriver 初始化成功 (浏览器: {browser})")
            
        except Exception as e:
            self.logger.error(f"WebDriver 初始化失败: {e}")
            raise
    
    def open_url(self, url: str):
        """
        打开指定URL
        
        Args:
            url: 目标网页URL
        """
        if not self.driver:
            self._setup_driver()
            
        try:
            self.logger.info(f"正在打开网页: {url}")
            self.driver.get(url)
            self.logger.info("网页加载完成")
        except Exception as e:
            self.logger.error(f"打开网页失败: {e}")
            raise
    
    def _find_element(self, xpath: str, timeout: Optional[int] = None) -> bool:
        """
        查找页面元素
        
        Args:
            xpath: 元素XPath
            timeout: 超时时间（秒）
            
        Returns:
            bool: 是否找到元素
        """
        if timeout is None:
            timeout = self.config.get('wait_timeout', 10)
            
        try:
            wait = WebDriverWait(self.driver, timeout)
            wait.until(EC.presence_of_element_located((By.XPATH, xpath)))
            return True
        except TimeoutException:
            return False
        except Exception as e:
            self.logger.error(f"查找元素时出错: {e}")
            return False
    
    def _click_element(self, xpath: str, element_name: str) -> bool:
        """
        点击页面元素
        
        Args:
            xpath: 元素XPath
            element_name: 元素名称（用于日志）
            
        Returns:
            bool: 是否点击成功
        """
        try:
            # 等待元素可点击
            element = self.wait.until(EC.element_to_be_clickable((By.XPATH, xpath)))
            element.click()
            self.logger.info(f"成功点击元素: {element_name}")
            return True
            
        except TimeoutException:
            self.logger.error(f"元素不可点击或超时: {element_name}")
            return False
        except Exception as e:
            self.logger.error(f"点击元素失败 {element_name}: {e}")
            return False

    def execute_click_sequence(self):
        """
        执行完整的点击序列

        Returns:
            bool: 是否执行成功
        """
        if self.is_executing:
            self.logger.warning("点击序列正在执行中，跳过本次触发")
            return False

        if not self.driver:
            self.logger.error("WebDriver 未初始化")
            return False

        self.is_executing = True
        click_sequence = self.config.get('click_sequence', [])

        if not click_sequence:
            self.logger.warning("未配置点击序列")
            self.is_executing = False
            return False

        try:
            self.logger.info("开始执行点击序列")

            for i, element_config in enumerate(click_sequence):
                element_name = element_config.get('name', f'元素{i+1}')
                xpath = element_config.get('xpath')
                description = element_config.get('description', '')

                if not xpath:
                    self.logger.error(f"元素 {element_name} 缺少XPath配置")
                    self._cancel_sequence("配置错误")
                    return False

                self.logger.info(f"步骤 {i+1}: {description}")

                # 第一个元素：直接点击
                if i == 0:
                    if not self._click_element(xpath, element_name):
                        self._cancel_sequence(f"无法点击第一个元素: {element_name}")
                        return False

                    # 点击后稍作等待
                    time.sleep(0.5)

                # 第二个及后续元素：先检测是否存在，再点击
                else:
                    # 特别处理第二个元素的检测逻辑
                    if i == 1:
                        self.logger.info(f"等待第二个元素出现: {element_name}")
                        if not self._find_element(xpath):
                            self._cancel_sequence(f"第二个元素未在超时时间内出现: {element_name}")
                            return False

                    # 点击元素
                    if not self._click_element(xpath, element_name):
                        self._cancel_sequence(f"无法点击元素: {element_name}")
                        return False

                    # 点击后稍作等待
                    time.sleep(0.5)

            self.logger.info("点击序列执行完成")
            self.is_executing = False
            return True

        except Exception as e:
            self.logger.error(f"执行点击序列时出错: {e}")
            self._cancel_sequence("执行异常")
            return False

    def _cancel_sequence(self, reason: str):
        """
        取消点击序列执行

        Args:
            reason: 取消原因
        """
        self.logger.warning(f"取消点击序列执行: {reason}")
        self.is_executing = False

    def refresh_page(self):
        """刷新当前页面"""
        if self.driver:
            try:
                self.driver.refresh()
                self.logger.info("页面已刷新")
            except Exception as e:
                self.logger.error(f"刷新页面失败: {e}")

    def get_current_url(self) -> str:
        """获取当前页面URL"""
        if self.driver:
            return self.driver.current_url
        return ""

    def close(self):
        """关闭浏览器"""
        if self.driver:
            try:
                self.driver.quit()
                self.logger.info("浏览器已关闭")
            except Exception as e:
                self.logger.error(f"关闭浏览器时出错: {e}")
            finally:
                self.driver = None
                self.wait = None
