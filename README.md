# 自动化网页点击工具

一个基于 Python 3.13 和 Selenium WebDriver 的自动化网页点击工具，支持键盘触发和智能元素检测。

## 功能特点

- 🎯 **精确点击**: 使用 XPath 定位器精确定位网页元素
- ⌨️ **键盘触发**: 支持自定义触发键（F1-F12、空格键等）
- 🔍 **智能检测**: 点击第一个元素后自动检测第二个元素是否出现
- ⏱️ **超时保护**: 可配置的等待超时时间，避免无限等待
- 📝 **详细日志**: 完整的操作日志记录和错误提示
- 🔧 **易于配置**: JSON 配置文件，无需修改代码

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置说明

编辑 `config.json` 文件来配置您的点击序列：

```json
{
    "url": "https://example.com",
    "trigger_key": "f1",
    "wait_timeout": 10,
    "click_sequence": [
        {
            "name": "登录按钮",
            "xpath": "//button[@id='login-btn']",
            "description": "点击登录按钮"
        },
        {
            "name": "确认按钮",
            "xpath": "//button[@class='confirm-btn']",
            "description": "等待并点击确认按钮"
        }
    ],
    "browser": "chrome",
    "headless": false,
    "implicit_wait": 3
}
```

### 配置参数说明

- **url**: 目标网页地址
- **trigger_key**: 触发键（支持 f1-f12, space, enter, esc, tab 等）
- **wait_timeout**: 等待元素出现的超时时间（秒）
- **click_sequence**: 点击序列配置数组
  - **name**: 元素名称（用于日志显示）
  - **xpath**: 元素的 XPath 定位器
  - **description**: 操作描述
- **browser**: 浏览器类型（chrome 或 firefox）
- **headless**: 是否无头模式运行
- **implicit_wait**: 隐式等待时间（秒）

## 使用方法

1. **配置设置**: 编辑 `config.json` 文件，设置目标网页和点击序列
2. **启动程序**: 运行 `python main.py`
3. **触发执行**: 按下配置的触发键执行点击序列
4. **退出程序**: 按 `Ctrl+C` 退出

## 核心逻辑

### 点击序列执行流程

1. **第一个元素**: 直接尝试点击
2. **第二个元素**: 
   - 等待元素在页面上出现（最多等待 `wait_timeout` 秒）
   - 如果超时未出现，取消整个点击序列
   - 如果出现，则点击该元素
3. **后续元素**: 依次点击剩余元素

### 错误处理

- 如果任何步骤失败，整个点击序列将被取消
- 详细的错误信息会记录在日志中
- 程序继续运行，等待下次触发

## 获取 XPath 的方法

### Chrome 浏览器
1. 右键点击目标元素 → "检查"
2. 在开发者工具中右键选中的元素
3. 选择 "Copy" → "Copy XPath"

### Firefox 浏览器
1. 右键点击目标元素 → "检查元素"
2. 在开发者工具中右键选中的元素
3. 选择 "复制" → "XPath"

## 日志文件

程序运行时会生成 `auto_clicker.log` 日志文件，包含：
- 程序启动和配置信息
- 点击序列执行过程
- 错误和警告信息
- 元素检测结果

## 注意事项

1. **元素定位**: 确保 XPath 定位器准确且唯一
2. **页面加载**: 确保目标网页完全加载后再执行点击
3. **权限问题**: 某些网站可能有反自动化机制
4. **浏览器版本**: 确保 Chrome 或 Firefox 浏览器已安装

## 故障排除

### 常见问题

1. **WebDriver 初始化失败**
   - 检查浏览器是否已安装
   - 检查网络连接（需要下载驱动）

2. **元素找不到**
   - 验证 XPath 是否正确
   - 检查页面是否完全加载
   - 尝试增加等待时间

3. **键盘监听不工作**
   - 确保程序有足够的权限
   - 检查触发键配置是否正确

## 技术栈

- Python 3.13
- Selenium WebDriver 4.15.2
- pynput 1.7.6 (键盘监听)
- webdriver-manager 4.0.1 (自动驱动管理)

## 许可证

本项目仅供学习和研究使用，请遵守相关网站的使用条款。
