"""
自动化网页点击工具
主程序入口
"""

import json
import logging
import sys
import time
import threading
from pathlib import Path
from typing import Dict, Optional

from web_clicker import WebClicker
from keyboard_listener import KeyboardListener


class AutoClickerApp:
    """自动点击应用主类"""
    
    def __init__(self, config_path: str = "config.json"):
        """
        初始化应用
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config: Dict = {}
        self.web_clicker: Optional[WebClicker] = None
        self.keyboard_listener: Optional[KeyboardListener] = None
        self.running = False
        
        # 设置日志
        self._setup_logging()
        self.logger = logging.getLogger(__name__)
        
    def _setup_logging(self):
        """设置日志配置"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler('auto_clicker.log', encoding='utf-8')
            ]
        )
    
    def load_config(self) -> bool:
        """
        加载配置文件
        
        Returns:
            bool: 是否加载成功
        """
        try:
            config_file = Path(self.config_path)
            if not config_file.exists():
                self.logger.error(f"配置文件不存在: {self.config_path}")
                return False
                
            with open(config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
                
            self.logger.info("配置文件加载成功")
            return True
            
        except json.JSONDecodeError as e:
            self.logger.error(f"配置文件格式错误: {e}")
            return False
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return False
    
    def validate_config(self) -> bool:
        """
        验证配置文件
        
        Returns:
            bool: 配置是否有效
        """
        required_fields = ['url', 'trigger_key', 'click_sequence']
        
        for field in required_fields:
            if field not in self.config:
                self.logger.error(f"配置文件缺少必需字段: {field}")
                return False
        
        if not self.config['click_sequence']:
            self.logger.error("点击序列不能为空")
            return False
            
        # 验证点击序列配置
        for i, element in enumerate(self.config['click_sequence']):
            if 'xpath' not in element:
                self.logger.error(f"点击序列第{i+1}个元素缺少xpath字段")
                return False
                
        self.logger.info("配置验证通过")
        return True
    
    def _on_trigger_key_pressed(self):
        """触发键按下时的回调函数"""
        if self.web_clicker:
            self.logger.info("检测到触发键，开始执行点击序列")
            # 在新线程中执行点击序列，避免阻塞键盘监听
            threading.Thread(
                target=self.web_clicker.execute_click_sequence,
                daemon=True
            ).start()
    
    def initialize_components(self):
        """初始化各个组件"""
        try:
            # 初始化网页点击器
            self.web_clicker = WebClicker(self.config)
            
            # 打开目标网页
            url = self.config['url']
            self.web_clicker.open_url(url)
            
            # 初始化键盘监听器
            trigger_key = self.config['trigger_key']
            self.keyboard_listener = KeyboardListener(
                trigger_key=trigger_key,
                callback=self._on_trigger_key_pressed
            )
            
            self.logger.info("组件初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"组件初始化失败: {e}")
            return False
    
    def start(self):
        """启动应用"""
        self.logger.info("=== 自动化网页点击工具启动 ===")
        
        # 加载和验证配置
        if not self.load_config():
            return False
            
        if not self.validate_config():
            return False
        
        # 显示配置信息
        self._display_config()
        
        # 初始化组件
        if not self.initialize_components():
            return False
        
        # 启动键盘监听
        self.keyboard_listener.start()
        
        self.running = True
        self.logger.info("应用启动成功")
        self.logger.info(f"按下 {self.config['trigger_key'].upper()} 键执行点击序列")
        self.logger.info("按 Ctrl+C 退出程序")
        
        return True
    
    def _display_config(self):
        """显示当前配置信息"""
        self.logger.info("=== 当前配置 ===")
        self.logger.info(f"目标网页: {self.config['url']}")
        self.logger.info(f"触发按键: {self.config['trigger_key'].upper()}")
        self.logger.info(f"等待超时: {self.config.get('wait_timeout', 10)}秒")
        self.logger.info(f"浏览器: {self.config.get('browser', 'chrome')}")
        
        self.logger.info("点击序列:")
        for i, element in enumerate(self.config['click_sequence']):
            name = element.get('name', f'元素{i+1}')
            description = element.get('description', '')
            self.logger.info(f"  {i+1}. {name}: {description}")
        
        self.logger.info("================")
    
    def run(self):
        """运行主循环"""
        try:
            while self.running:
                time.sleep(0.1)
        except KeyboardInterrupt:
            self.logger.info("接收到退出信号")
            self.stop()
    
    def stop(self):
        """停止应用"""
        self.logger.info("正在停止应用...")
        self.running = False
        
        if self.keyboard_listener:
            self.keyboard_listener.stop()
            
        if self.web_clicker:
            self.web_clicker.close()
            
        self.logger.info("应用已停止")


def main():
    """主函数"""
    app = AutoClickerApp()
    
    if app.start():
        app.run()
    else:
        print("应用启动失败，请检查配置和日志")
        sys.exit(1)


if __name__ == "__main__":
    main()
