"""
自动化网页点击工具 - GUI主程序
现代化图形用户界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import threading
import logging
import queue
import sys
from pathlib import Path
from typing import Dict, List, Optional, Any

from gui_theme import ModernTheme
from gui_components import StatusIndicator, ModernButton, ModernEntry, ClickSequenceList
from main import AutoClickerApp


class LogHandler(logging.Handler):
    """自定义日志处理器，用于GUI显示"""
    
    def __init__(self, log_queue: queue.Queue):
        super().__init__()
        self.log_queue = log_queue
    
    def emit(self, record):
        """发送日志记录到队列"""
        try:
            msg = self.format(record)
            self.log_queue.put(msg)
        except Exception:
            pass


class ElementEditDialog:
    """元素编辑对话框"""
    
    def __init__(self, parent, theme: ModernTheme, element: Dict[str, str] = None):
        self.parent = parent
        self.theme = theme
        self.element = element or {}
        self.result = None
        
        self._create_dialog()
    
    def _create_dialog(self):
        """创建对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("编辑元素" if self.element else "添加元素")
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (400 // 2)
        self.dialog.geometry(f"500x400+{x}+{y}")
        
        self.theme.apply_frame_style(self.dialog)
        self.dialog.configure(bg=self.theme.get_color('bg_primary'))
        
        self._create_content()
    
    def _create_content(self):
        """创建对话框内容"""
        # 主容器
        main_frame = tk.Frame(self.dialog)
        self.theme.apply_frame_style(main_frame)
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # 标题
        title_label = tk.Label(main_frame, text="编辑元素" if self.element else "添加元素")
        self.theme.apply_label_style(title_label, 'heading')
        title_label.pack(anchor='w', pady=(0, 20))
        
        # 表单
        form_frame = tk.Frame(main_frame)
        self.theme.apply_frame_style(form_frame)
        form_frame.pack(fill='x', pady=(0, 20))
        
        # 名称输入
        self.name_entry = ModernEntry(form_frame, self.theme, 
                                     label="元素名称", placeholder="例如：登录按钮")
        self.name_entry.pack(fill='x', pady=(0, 15))
        if self.element.get('name'):
            self.name_entry.set(self.element['name'])
        
        # XPath输入
        self.xpath_entry = ModernEntry(form_frame, self.theme,
                                      label="XPath定位器", placeholder="例如：//button[@id='login']")
        self.xpath_entry.pack(fill='x', pady=(0, 15))
        if self.element.get('xpath'):
            self.xpath_entry.set(self.element['xpath'])
        
        # 描述输入
        self.desc_entry = ModernEntry(form_frame, self.theme,
                                     label="操作描述", placeholder="例如：点击登录按钮")
        self.desc_entry.pack(fill='x', pady=(0, 15))
        if self.element.get('description'):
            self.desc_entry.set(self.element['description'])
        
        # XPath测试按钮
        test_btn = ModernButton(form_frame, self.theme, 'secondary',
                               text="测试XPath", command=self._test_xpath)
        test_btn.pack(anchor='w', pady=(0, 15))
        
        # 按钮组
        btn_frame = tk.Frame(main_frame)
        self.theme.apply_frame_style(btn_frame)
        btn_frame.pack(fill='x')
        
        cancel_btn = ModernButton(btn_frame, self.theme, 'secondary',
                                 text="取消", command=self._cancel)
        cancel_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        save_btn = ModernButton(btn_frame, self.theme, 'primary',
                               text="保存", command=self._save)
        save_btn.pack(side=tk.RIGHT)
    
    def _test_xpath(self):
        """测试XPath"""
        xpath = self.xpath_entry.get()
        if not xpath:
            messagebox.showwarning("警告", "请先输入XPath")
            return
        
        # 这里可以添加实际的XPath测试逻辑
        messagebox.showinfo("提示", "XPath测试功能需要在浏览器打开时使用")
    
    def _save(self):
        """保存元素"""
        name = self.name_entry.get().strip()
        xpath = self.xpath_entry.get().strip()
        description = self.desc_entry.get().strip()
        
        if not name:
            messagebox.showerror("错误", "请输入元素名称")
            return
        
        if not xpath:
            messagebox.showerror("错误", "请输入XPath定位器")
            return
        
        self.result = {
            'name': name,
            'xpath': xpath,
            'description': description
        }
        
        self.dialog.destroy()
    
    def _cancel(self):
        """取消编辑"""
        self.dialog.destroy()
    
    def show(self) -> Optional[Dict[str, str]]:
        """显示对话框并返回结果"""
        self.dialog.wait_window()
        return self.result


class AutoClickerGUI:
    """自动点击工具GUI主类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.theme = ModernTheme(dark_mode=False)
        self.app: Optional[AutoClickerApp] = None
        self.config: Dict[str, Any] = {}
        self.log_queue = queue.Queue()
        
        # 设置日志
        self._setup_logging()
        
        # 初始化GUI
        self._setup_window()
        self._create_widgets()
        self._load_config()
        
        # 启动日志更新
        self._update_logs()
    
    def _setup_logging(self):
        """设置日志系统"""
        # 创建自定义日志处理器
        self.log_handler = LogHandler(self.log_queue)
        self.log_handler.setFormatter(
            logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        )
        
        # 添加到根日志器
        root_logger = logging.getLogger()
        root_logger.addHandler(self.log_handler)
        root_logger.setLevel(logging.INFO)
    
    def _setup_window(self):
        """设置主窗口"""
        self.root.title("自动化网页点击工具")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        
        # 设置图标（如果有的话）
        try:
            # self.root.iconbitmap("icon.ico")
            pass
        except:
            pass
        
        # 应用主题
        self.theme.apply_frame_style(self.root)
        self.root.configure(bg=self.theme.get_color('bg_primary'))
        
        # 关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主容器
        main_container = tk.Frame(self.root)
        self.theme.apply_frame_style(main_container)
        main_container.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 创建左右分栏
        self._create_left_panel(main_container)
        self._create_right_panel(main_container)
    
    def _create_left_panel(self, parent):
        """创建左侧面板"""
        left_frame = tk.Frame(parent)
        self.theme.apply_frame_style(left_frame)
        left_frame.pack(side=tk.LEFT, fill='both', expand=True, padx=(0, 5))
        
        # 配置区域
        self._create_config_section(left_frame)
        
        # 点击序列区域
        self._create_sequence_section(left_frame)
    
    def _create_right_panel(self, parent):
        """创建右侧面板"""
        right_frame = tk.Frame(parent)
        self.theme.apply_frame_style(right_frame)
        right_frame.pack(side=tk.RIGHT, fill='both', padx=(5, 0))
        right_frame.configure(width=300)
        
        # 控制区域
        self._create_control_section(right_frame)
        
        # 日志区域
        self._create_log_section(right_frame)
    
    def _create_config_section(self, parent):
        """创建配置区域"""
        config_frame = self.theme.create_card_frame(parent)
        config_frame.pack(fill='x', pady=(0, 10))
        
        # 内容容器
        content_frame = tk.Frame(config_frame)
        self.theme.apply_frame_style(content_frame)
        content_frame.pack(fill='x', padx=15, pady=15)
        
        # 标题
        title_label = tk.Label(content_frame, text="基本配置")
        self.theme.apply_label_style(title_label, 'heading')
        title_label.pack(anchor='w', pady=(0, 15))
        
        # URL输入
        self.url_entry = ModernEntry(content_frame, self.theme,
                                    label="目标网页URL", placeholder="https://example.com")
        self.url_entry.pack(fill='x', pady=(0, 10))
        
        # 设置行
        settings_frame = tk.Frame(content_frame)
        self.theme.apply_frame_style(settings_frame)
        settings_frame.pack(fill='x', pady=(0, 10))
        
        # 触发键选择
        trigger_frame = tk.Frame(settings_frame)
        self.theme.apply_frame_style(trigger_frame)
        trigger_frame.pack(side=tk.LEFT, fill='x', expand=True, padx=(0, 10))
        
        trigger_label = tk.Label(trigger_frame, text="触发按键")
        self.theme.apply_label_style(trigger_label, 'small')
        trigger_label.pack(anchor='w', pady=(0, 4))
        
        self.trigger_var = tk.StringVar(value="f1")
        self.trigger_combo = ttk.Combobox(trigger_frame, textvariable=self.trigger_var,
                                         values=['f1', 'f2', 'f3', 'f4', 'f5', 'f6',
                                                'f7', 'f8', 'f9', 'f10', 'f11', 'f12',
                                                'space', 'enter', 'esc', 'tab'],
                                         state='readonly')
        self.trigger_combo.pack(fill='x', ipady=4)
        
        # 浏览器选择
        browser_frame = tk.Frame(settings_frame)
        self.theme.apply_frame_style(browser_frame)
        browser_frame.pack(side=tk.LEFT, fill='x', expand=True)
        
        browser_label = tk.Label(browser_frame, text="浏览器")
        self.theme.apply_label_style(browser_label, 'small')
        browser_label.pack(anchor='w', pady=(0, 4))
        
        self.browser_var = tk.StringVar(value="chrome")
        self.browser_combo = ttk.Combobox(browser_frame, textvariable=self.browser_var,
                                         values=['chrome', 'firefox'], state='readonly')
        self.browser_combo.pack(fill='x', ipady=4)
        
        # 超时设置
        timeout_frame = tk.Frame(content_frame)
        self.theme.apply_frame_style(timeout_frame)
        timeout_frame.pack(fill='x')
        
        timeout_label = tk.Label(timeout_frame, text="等待超时时间（秒）")
        self.theme.apply_label_style(timeout_label, 'small')
        timeout_label.pack(anchor='w', pady=(0, 4))
        
        self.timeout_var = tk.IntVar(value=10)
        self.timeout_scale = tk.Scale(timeout_frame, from_=5, to=30, orient=tk.HORIZONTAL,
                                     variable=self.timeout_var, length=200)
        self.timeout_scale.pack(anchor='w')
    
    def _create_sequence_section(self, parent):
        """创建点击序列区域"""
        sequence_frame = self.theme.create_card_frame(parent)
        sequence_frame.pack(fill='both', expand=True)
        
        # 内容容器
        content_frame = tk.Frame(sequence_frame)
        self.theme.apply_frame_style(content_frame)
        content_frame.pack(fill='both', expand=True, padx=15, pady=15)
        
        # 点击序列列表
        self.sequence_list = ClickSequenceList(content_frame, self.theme)
        self.sequence_list.pack(fill='both', expand=True)
        
        # 设置回调
        self.sequence_list.set_callbacks(
            edit_callback=self._edit_element,
            delete_callback=self._delete_element
        )

    def _create_control_section(self, parent):
        """创建控制区域"""
        control_frame = self.theme.create_card_frame(parent)
        control_frame.pack(fill='x', pady=(0, 10))

        # 内容容器
        content_frame = tk.Frame(control_frame)
        self.theme.apply_frame_style(content_frame)
        content_frame.pack(fill='x', padx=15, pady=15)

        # 标题
        title_label = tk.Label(content_frame, text="控制面板")
        self.theme.apply_label_style(title_label, 'heading')
        title_label.pack(anchor='w', pady=(0, 15))

        # 状态指示器
        self.status_indicator = StatusIndicator(content_frame, self.theme)
        self.status_indicator.pack(fill='x', pady=(0, 15))

        # 主要控制按钮
        self.start_btn = ModernButton(content_frame, self.theme, 'success',
                                     text="启动", command=self._start_app)
        self.start_btn.pack(fill='x', pady=(0, 8))

        self.stop_btn = ModernButton(content_frame, self.theme, 'error',
                                    text="停止", command=self._stop_app, state='disabled')
        self.stop_btn.pack(fill='x', pady=(0, 8))

        # 其他功能按钮
        btn_frame = tk.Frame(content_frame)
        self.theme.apply_frame_style(btn_frame)
        btn_frame.pack(fill='x', pady=(10, 0))

        test_btn = ModernButton(btn_frame, self.theme, 'secondary',
                               text="测试配置", command=self._test_config)
        test_btn.pack(fill='x', pady=(0, 4))

        save_btn = ModernButton(btn_frame, self.theme, 'secondary',
                               text="保存配置", command=self._save_config)
        save_btn.pack(fill='x', pady=(0, 4))

        load_btn = ModernButton(btn_frame, self.theme, 'secondary',
                               text="加载配置", command=self._load_config_file)
        load_btn.pack(fill='x', pady=(0, 4))

        # 主题切换
        theme_btn = ModernButton(btn_frame, self.theme, 'secondary',
                                text="切换主题", command=self._toggle_theme)
        theme_btn.pack(fill='x', pady=(0, 4))

    def _create_log_section(self, parent):
        """创建日志区域"""
        log_frame = self.theme.create_card_frame(parent)
        log_frame.pack(fill='both', expand=True)

        # 内容容器
        content_frame = tk.Frame(log_frame)
        self.theme.apply_frame_style(content_frame)
        content_frame.pack(fill='both', expand=True, padx=15, pady=15)

        # 标题
        title_frame = tk.Frame(content_frame)
        self.theme.apply_frame_style(title_frame)
        title_frame.pack(fill='x', pady=(0, 10))

        title_label = tk.Label(title_frame, text="运行日志")
        self.theme.apply_label_style(title_label, 'heading')
        title_label.pack(side=tk.LEFT)

        clear_btn = ModernButton(title_frame, self.theme, 'secondary',
                                text="清空", command=self._clear_logs)
        clear_btn.pack(side=tk.RIGHT)

        # 日志文本框
        log_text_frame = tk.Frame(content_frame)
        self.theme.apply_frame_style(log_text_frame)
        log_text_frame.pack(fill='both', expand=True)

        self.log_text = tk.Text(log_text_frame, wrap=tk.WORD, state='disabled',
                               font=self.theme.get_font('monospace'))
        self.log_text.configure(
            bg=self.theme.get_color('bg_surface'),
            fg=self.theme.get_color('text_primary'),
            insertbackground=self.theme.get_color('text_primary'),
            selectbackground=self.theme.get_color('accent'),
            selectforeground='#FFFFFF'
        )

        log_scrollbar = ttk.Scrollbar(log_text_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        self.log_text.pack(side="left", fill="both", expand=True)
        log_scrollbar.pack(side="right", fill="y")

    def _edit_element(self, element: Optional[Dict[str, str]], index: int):
        """编辑元素"""
        dialog = ElementEditDialog(self.root, self.theme, element)
        result = dialog.show()

        if result:
            if element is None:
                # 添加新元素
                self.config.setdefault('click_sequence', []).append(result)
            else:
                # 编辑现有元素
                self.config['click_sequence'][index] = result

            self._update_sequence_display()

    def _delete_element(self, index: int):
        """删除元素"""
        if 'click_sequence' in self.config and index < len(self.config['click_sequence']):
            del self.config['click_sequence'][index]
            self._update_sequence_display()

    def _update_sequence_display(self):
        """更新序列显示"""
        sequence = self.config.get('click_sequence', [])
        self.sequence_list.update_items(sequence)

    def _start_app(self):
        """启动应用"""
        try:
            # 更新配置
            self._update_config_from_ui()

            # 验证配置
            if not self._validate_config():
                return

            # 创建应用实例
            self.app = AutoClickerApp()
            self.app.config = self.config.copy()

            # 在新线程中启动
            def start_thread():
                try:
                    if self.app.initialize_components():
                        self.app.keyboard_listener.start()
                        self.app.running = True

                        # 更新UI状态
                        self.root.after(0, lambda: self._update_ui_state(True))

                        # 运行主循环
                        while self.app.running:
                            threading.Event().wait(0.1)
                    else:
                        self.root.after(0, lambda: messagebox.showerror("错误", "应用初始化失败"))

                except Exception as e:
                    self.root.after(0, lambda: messagebox.showerror("错误", f"启动失败: {str(e)}"))
                finally:
                    self.root.after(0, lambda: self._update_ui_state(False))

            threading.Thread(target=start_thread, daemon=True).start()

        except Exception as e:
            messagebox.showerror("错误", f"启动失败: {str(e)}")

    def _stop_app(self):
        """停止应用"""
        if self.app:
            self.app.stop()
            self.app = None
        self._update_ui_state(False)

    def _update_ui_state(self, running: bool):
        """更新UI状态"""
        if running:
            self.start_btn.configure(state='disabled')
            self.stop_btn.configure(state='normal')
            self.status_indicator.set_status('running', '运行中')
        else:
            self.start_btn.configure(state='normal')
            self.stop_btn.configure(state='disabled')
            self.status_indicator.set_status('idle', '已停止')

    def _test_config(self):
        """测试配置"""
        self._update_config_from_ui()
        if self._validate_config():
            messagebox.showinfo("测试结果", "配置验证通过！")

    def _validate_config(self) -> bool:
        """验证配置"""
        if not self.config.get('url'):
            messagebox.showerror("配置错误", "请输入目标网页URL")
            return False

        if not self.config.get('click_sequence'):
            messagebox.showerror("配置错误", "请至少添加一个点击元素")
            return False

        for i, element in enumerate(self.config['click_sequence']):
            if not element.get('xpath'):
                messagebox.showerror("配置错误", f"第{i+1}个元素缺少XPath")
                return False

        return True

    def _update_config_from_ui(self):
        """从UI更新配置"""
        self.config.update({
            'url': self.url_entry.get(),
            'trigger_key': self.trigger_var.get(),
            'browser': self.browser_var.get(),
            'wait_timeout': self.timeout_var.get(),
            'headless': False,
            'implicit_wait': 3
        })

    def _save_config(self):
        """保存配置到文件"""
        self._update_config_from_ui()

        filename = filedialog.asksaveasfilename(
            title="保存配置文件",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.config, f, ensure_ascii=False, indent=2)
                messagebox.showinfo("成功", "配置已保存")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {str(e)}")

    def _load_config_file(self):
        """从文件加载配置"""
        filename = filedialog.askopenfilename(
            title="选择配置文件",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                self._update_ui_from_config()
                messagebox.showinfo("成功", "配置已加载")
            except Exception as e:
                messagebox.showerror("错误", f"加载失败: {str(e)}")

    def _load_config(self):
        """加载默认配置文件"""
        try:
            config_file = Path("config.json")
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                self._update_ui_from_config()
        except Exception as e:
            # 使用默认配置
            self.config = {
                'url': '',
                'trigger_key': 'f1',
                'browser': 'chrome',
                'wait_timeout': 10,
                'click_sequence': [],
                'headless': False,
                'implicit_wait': 3
            }

    def _update_ui_from_config(self):
        """从配置更新UI"""
        self.url_entry.set(self.config.get('url', ''))
        self.trigger_var.set(self.config.get('trigger_key', 'f1'))
        self.browser_var.set(self.config.get('browser', 'chrome'))
        self.timeout_var.set(self.config.get('wait_timeout', 10))
        self._update_sequence_display()

    def _toggle_theme(self):
        """切换主题"""
        self.theme.toggle_theme()
        self._apply_theme_to_all_widgets()
        messagebox.showinfo("主题", f"已切换到{'深色' if self.theme.dark_mode else '浅色'}主题")

    def _apply_theme_to_all_widgets(self):
        """应用主题到所有组件"""
        # 这里可以递归应用主题到所有子组件
        # 为了简化，我们重新启动应用来应用新主题
        pass

    def _clear_logs(self):
        """清空日志"""
        self.log_text.configure(state='normal')
        self.log_text.delete(1.0, tk.END)
        self.log_text.configure(state='disabled')

    def _update_logs(self):
        """更新日志显示"""
        try:
            while True:
                try:
                    log_msg = self.log_queue.get_nowait()
                    self.log_text.configure(state='normal')
                    self.log_text.insert(tk.END, log_msg + '\n')
                    self.log_text.see(tk.END)
                    self.log_text.configure(state='disabled')
                except queue.Empty:
                    break
        except Exception:
            pass

        # 每100ms检查一次日志队列
        self.root.after(100, self._update_logs)

    def _on_closing(self):
        """窗口关闭事件"""
        if self.app and self.app.running:
            if messagebox.askokcancel("退出", "程序正在运行，确定要退出吗？"):
                self._stop_app()
                self.root.destroy()
        else:
            self.root.destroy()

    def run(self):
        """运行GUI应用"""
        self.root.mainloop()


def main():
    """GUI主函数"""
    try:
        app = AutoClickerGUI()
        app.run()
    except Exception as e:
        messagebox.showerror("错误", f"应用启动失败: {str(e)}")


if __name__ == "__main__":
    main()
