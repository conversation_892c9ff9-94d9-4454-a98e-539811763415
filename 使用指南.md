# 自动化网页点击工具 - 详细使用指南

## 🚀 快速开始

### 1. 环境准备
- **Python 3.8+**: 确保已安装Python 3.8或更高版本
- **浏览器**: Chrome 或 Firefox 浏览器

### 2. 安装依赖
```bash
# 方法1: 使用安装脚本
python install.py

# 方法2: 手动安装
pip install -r requirements.txt
```

### 3. 启动程序
```bash
# 方法1: 使用启动脚本
python start.py

# 方法2: Windows用户双击
启动工具.bat

# 方法3: 直接运行
python main.py
```

## 📋 配置详解

### config.json 配置文件结构

```json
{
    "url": "目标网页地址",
    "trigger_key": "触发按键",
    "wait_timeout": 等待超时时间(秒),
    "click_sequence": [
        {
            "name": "元素名称",
            "xpath": "XPath定位器",
            "description": "操作描述"
        }
    ],
    "browser": "浏览器类型",
    "headless": false,
    "implicit_wait": 隐式等待时间
}
```

### 支持的触发按键
- **功能键**: f1, f2, f3, ..., f12
- **特殊键**: space, enter, esc, tab
- **字母键**: a, b, c, ..., z
- **数字键**: 0, 1, 2, ..., 9

### 浏览器选项
- **chrome**: Google Chrome (推荐)
- **firefox**: Mozilla Firefox

## 🎯 XPath 获取方法

### Chrome 浏览器
1. 打开目标网页
2. 右键点击要操作的元素
3. 选择 "检查" (Inspect)
4. 在开发者工具中，右键点击高亮的HTML元素
5. 选择 "Copy" → "Copy XPath"

### Firefox 浏览器
1. 打开目标网页
2. 右键点击要操作的元素
3. 选择 "检查元素" (Inspect Element)
4. 在开发者工具中，右键点击高亮的HTML元素
5. 选择 "复制" → "XPath"

### XPath 优化建议
- 尽量使用ID或class属性: `//button[@id='submit-btn']`
- 避免使用绝对路径: `/html/body/div[1]/div[2]/button`
- 使用相对路径: `//div[@class='form']//button`
- 包含文本的元素: `//button[contains(text(), '提交')]`

## 🔧 实际使用示例

### 示例1: 简单表单填写
```json
{
    "url": "https://example.com/form",
    "trigger_key": "f1",
    "wait_timeout": 10,
    "click_sequence": [
        {
            "name": "用户名输入框",
            "xpath": "//input[@name='username']",
            "description": "点击用户名输入框"
        },
        {
            "name": "密码输入框",
            "xpath": "//input[@name='password']",
            "description": "点击密码输入框"
        },
        {
            "name": "登录按钮",
            "xpath": "//button[@type='submit']",
            "description": "点击登录按钮"
        }
    ],
    "browser": "chrome",
    "headless": false,
    "implicit_wait": 3
}
```

### 示例2: 电商网站操作
```json
{
    "url": "https://shop.example.com",
    "trigger_key": "space",
    "wait_timeout": 15,
    "click_sequence": [
        {
            "name": "搜索框",
            "xpath": "//input[@placeholder='搜索商品']",
            "description": "点击搜索框"
        },
        {
            "name": "搜索按钮",
            "xpath": "//button[@class='search-btn']",
            "description": "点击搜索按钮"
        },
        {
            "name": "第一个商品",
            "xpath": "//div[@class='product-item'][1]",
            "description": "点击第一个商品"
        },
        {
            "name": "加入购物车",
            "xpath": "//button[contains(text(), '加入购物车')]",
            "description": "点击加入购物车按钮"
        }
    ],
    "browser": "chrome",
    "headless": false,
    "implicit_wait": 5
}
```

## 🛠️ 故障排除

### 常见问题及解决方案

#### 1. WebDriver 初始化失败
**问题**: `WebDriver 初始化失败`
**解决方案**:
- 确保Chrome或Firefox浏览器已安装
- 检查网络连接（需要下载驱动程序）
- 尝试切换浏览器类型

#### 2. 元素找不到
**问题**: `元素不可点击或超时`
**解决方案**:
- 验证XPath是否正确
- 增加等待时间 (`wait_timeout`)
- 检查页面是否完全加载
- 确认元素在当前页面可见

#### 3. 键盘监听不工作
**问题**: 按键无响应
**解决方案**:
- 确保程序有足够权限
- 检查触发键配置是否正确
- 尝试使用管理员权限运行

#### 4. 点击序列中断
**问题**: 第二个元素检测失败
**解决方案**:
- 检查第一个元素点击后页面变化
- 调整等待时间
- 验证第二个元素的XPath

### 调试技巧

#### 1. 启用详细日志
程序会自动生成 `auto_clicker.log` 日志文件，包含详细的执行信息。

#### 2. 使用测试模式
```bash
python test_basic.py
```

#### 3. 非无头模式调试
设置 `"headless": false` 可以看到浏览器操作过程。

#### 4. 逐步测试
先配置1-2个元素进行测试，确认无误后再添加更多元素。

## 📝 最佳实践

### 1. 配置管理
- 为不同网站创建不同的配置文件
- 使用描述性的元素名称
- 定期备份配置文件

### 2. XPath 编写
- 优先使用稳定的属性（id, class）
- 避免使用位置相关的选择器
- 测试XPath的唯一性

### 3. 时间设置
- 根据网站响应速度调整超时时间
- 为慢速网站增加等待时间
- 在元素间添加适当延迟

### 4. 安全考虑
- 遵守网站使用条款
- 避免过于频繁的操作
- 不要用于恶意目的

## 🔄 更新和维护

### 版本更新
```bash
# 更新依赖包
pip install --upgrade -r requirements.txt
```

### 配置备份
定期备份 `config.json` 文件，避免配置丢失。

### 日志清理
定期清理 `auto_clicker.log` 文件，避免占用过多磁盘空间。

## 📞 技术支持

如果遇到问题，请：
1. 查看日志文件 `auto_clicker.log`
2. 运行基础测试 `python test_basic.py`
3. 检查配置文件格式
4. 确认网络和浏览器环境

---

**注意**: 本工具仅供学习和合法用途使用，请遵守相关法律法规和网站使用条款。
