"""
GUI功能测试脚本
测试图形界面的各个组件和功能
"""

import tkinter as tk
import logging
import sys
from gui_theme import ModernTheme
from gui_components import StatusIndicator, ModernButton, ModernEntry, ClickSequenceList

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_theme():
    """测试主题功能"""
    logger.info("=== 测试主题功能 ===")
    
    try:
        # 测试浅色主题
        light_theme = ModernTheme(dark_mode=False)
        logger.info("✓ 浅色主题创建成功")
        
        # 测试深色主题
        dark_theme = ModernTheme(dark_mode=True)
        logger.info("✓ 深色主题创建成功")
        
        # 测试主题切换
        light_theme.toggle_theme()
        logger.info("✓ 主题切换功能正常")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 主题测试失败: {e}")
        return False

def test_components():
    """测试GUI组件"""
    logger.info("=== 测试GUI组件 ===")
    
    try:
        # 创建测试窗口
        root = tk.Tk()
        root.title("GUI组件测试")
        root.geometry("600x400")
        
        theme = ModernTheme()
        theme.apply_frame_style(root)
        root.configure(bg=theme.get_color('bg_primary'))
        
        # 测试状态指示器
        status = StatusIndicator(root, theme)
        status.pack(pady=10)
        status.set_status('running', '测试中')
        logger.info("✓ 状态指示器组件正常")
        
        # 测试现代化按钮
        btn = ModernButton(root, theme, 'primary', text="测试按钮")
        btn.pack(pady=10)
        logger.info("✓ 现代化按钮组件正常")
        
        # 测试现代化输入框
        entry = ModernEntry(root, theme, label="测试输入", placeholder="请输入测试内容")
        entry.pack(pady=10, padx=20, fill='x')
        logger.info("✓ 现代化输入框组件正常")
        
        # 测试点击序列列表
        sequence_list = ClickSequenceList(root, theme)
        sequence_list.pack(pady=10, padx=20, fill='both', expand=True)
        
        # 添加测试数据
        test_items = [
            {'name': '测试元素1', 'xpath': '//button[@id="test1"]', 'description': '测试描述1'},
            {'name': '测试元素2', 'xpath': '//button[@id="test2"]', 'description': '测试描述2'}
        ]
        sequence_list.update_items(test_items)
        logger.info("✓ 点击序列列表组件正常")
        
        # 显示窗口2秒后关闭
        root.after(2000, root.destroy)
        root.mainloop()
        
        logger.info("✓ 所有GUI组件测试通过")
        return True
        
    except Exception as e:
        logger.error(f"✗ GUI组件测试失败: {e}")
        return False

def test_gui_imports():
    """测试GUI模块导入"""
    logger.info("=== 测试GUI模块导入 ===")
    
    modules = [
        ('gui_theme', 'ModernTheme'),
        ('gui_components', 'StatusIndicator'),
        ('gui_components', 'ModernButton'),
        ('gui_components', 'ModernEntry'),
        ('gui_components', 'ClickSequenceList'),
        ('gui_main', 'AutoClickerGUI'),
        ('gui_wizard', 'ConfigWizard')
    ]
    
    success_count = 0
    
    for module_name, class_name in modules:
        try:
            module = __import__(module_name)
            getattr(module, class_name)
            logger.info(f"✓ {module_name}.{class_name}")
            success_count += 1
        except ImportError as e:
            logger.error(f"✗ {module_name}.{class_name} - 导入失败: {e}")
        except AttributeError as e:
            logger.error(f"✗ {module_name}.{class_name} - 类不存在: {e}")
        except Exception as e:
            logger.error(f"✗ {module_name}.{class_name} - 其他错误: {e}")
    
    logger.info(f"模块导入测试: {success_count}/{len(modules)} 成功")
    return success_count == len(modules)

def test_tkinter_availability():
    """测试tkinter可用性"""
    logger.info("=== 测试tkinter可用性 ===")
    
    try:
        import tkinter as tk
        from tkinter import ttk, messagebox, filedialog
        
        # 创建测试窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 测试基本组件
        frame = tk.Frame(root)
        label = tk.Label(frame, text="测试")
        button = tk.Button(frame, text="测试")
        entry = tk.Entry(frame)
        
        # 测试ttk组件
        ttk_button = ttk.Button(frame, text="TTK测试")
        ttk_combo = ttk.Combobox(frame, values=['test1', 'test2'])
        
        root.destroy()
        
        logger.info("✓ tkinter及相关模块可用")
        return True
        
    except ImportError as e:
        logger.error(f"✗ tkinter导入失败: {e}")
        return False
    except Exception as e:
        logger.error(f"✗ tkinter测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("开始GUI功能测试")
    
    tests = [
        ("tkinter可用性", test_tkinter_availability),
        ("GUI模块导入", test_gui_imports),
        ("主题功能", test_theme),
        ("GUI组件", test_components)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"测试 {test_name} 出现异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    logger.info(f"\n{'='*50}")
    logger.info("GUI测试结果汇总:")
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        logger.info(f"{test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    logger.info(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        logger.info("🎉 所有GUI测试通过！可以运行GUI程序了。")
        
        # 询问是否启动GUI
        try:
            import tkinter as tk
            from tkinter import messagebox
            
            root = tk.Tk()
            root.withdraw()
            
            if messagebox.askyesno("测试完成", "所有测试通过！是否现在启动GUI程序？"):
                root.destroy()
                from gui_main import main as gui_main
                gui_main()
            else:
                root.destroy()
                
        except Exception:
            logger.info("可以手动运行: python gui_main.py")
    else:
        logger.warning("⚠️ 部分GUI测试失败，请检查环境和依赖。")

if __name__ == "__main__":
    main()
